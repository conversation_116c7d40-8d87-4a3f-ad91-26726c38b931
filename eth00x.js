
function Enum(constantsList) {
    for (var i in constantsList) {
        this[constantsList[i]] = i;
    }
}

var commStates = new Enum(['IDLE', 'RESULT_SET', 'RESULT_GET'])
var connStates = new Enum(['IDLE', 'OK', 'KICK', 'TIMEOUT'])

var eth00x = {
	buffer				: [],
	relayStates			: [],
	systemName			: "ETH00X",
	feedbackName		: "raw_ETH00X",
	connectionJoin		: "d112",
	connectionString	: "s112",

	commState			: commStates.IDLE, //Wat was de vraag voor het antwoord binnenkwam.
	connState			: connStates.IDLE,
	connTimeout			: 0,

	relaisON 			: "\x01",
	relaisOFF 			: "\x00",

	requestInterval 	: "",

	init : function(){
		CF.log("eth00x.init")
		eth00x.commState = commStates.IDLE
		eth00x.connect(connStates.IDLE)
		CF.watch(CF.FeedbackMatchedEvent, eth00x.systemName, eth00x.feedbackName, eth00x.parseData);              // controller op feedback
    	CF.watch(CF.ConnectionStatusChangeEvent, eth00x.systemName, eth00x.onConnectionChange, true);
	   	CF.watch(CF.OrientationChangeEvent, eth00x.onOrientationChange, true);
	},

	switchRelais(input, state) {
		eth00x.send("\x31" + input + (state ? eth00x.relaisON : eth00x.relaisOFF) + "\x00\x00\x00\xD0") // Pulse 1000mS
	//	eth00x.send("\x31" + input + (state ? eth00x.relaisON : eth00x.relaisOFF) + "\x00\x00\x07\xD0") // Pulse 2000mS

	},

	onOrientationChange: function (pageName, newOrientation) {
    	CF.log("Orientation of page " + pageName + " changed to: " + newOrientation);
//    	CF.setSystemProperties(eth00x.systemName,{enabled: false});
//    	CF.setSystemProperties(eth00x.systemName,{enabled: true});
 	},

 	onConnectionChange : function(system, connected, remote) {
		if (connected) {
			CF.log("ETH Connected: " + "System " + system + " connected with " + remote);
    		CF.setJoin(eth00x.connectionJoin , 1);
    		CF.setJoin(eth00x.connectionString , remote);
	   		
	   		eth00x.requestInterval = setInterval(function(){eth00x.requestStatus();},250)		// Verstuur iedere 100 mS data, als data in buffer aanwezig

    	}
    	else{
    		CF.log(system + " not connected")
			CF.setJoin(eth00x.connectionJoin, 0);
    		CF.setJoin(eth00x.connectionString , "No Connection");
    		clearInterval(eth00x.requestInterval);
    	}
    },

    requestStatus : function (){
     	CF.send(eth00x.systemName, "\x33");
    	eth00x.commState = commStates.RESULT_GET;
    },

    send : function(data){
 		switch(eth00x.commState){
			case commStates.IDLE: //Als de zender vrij is meteeen gebruiken
				CF.send(eth00x.systemName, data);
				eth00x.commState = commStates.RESULT_SET
				break;
			default: //Anders commando naar buffer
				eth00x.buffer.push(data)
				break;
		}
 	},

	connect : function(immediateState){
 		if (immediateState){
			eth00x.connState = immediateState
		}
		
		switch(eth00x.connState){
 			
			case connStates.IDLE:
				eth00x.connState = connStates.KICK
				eth00x.connTimeout = setTimeout(function(){eth00x.connect()}, 3000);
				break;
			
			case connStates.OK: //Niets te doen behalve nieuwe poging starten
				clearTimeout(eth00x.connTimeout)
				eth00x.connState = connStates.KICK
				eth00x.connTimeout = setTimeout(function(){eth00x.connect()}, 3000);
				break;
			
			case connStates.KICK: //Tijd voor een update
				eth00x.connState = connStates.TIMEOUT
				if (eth00x.commState == commStates.IDLE){
					eth00x.requestStatus()
				}
				clearTimeout(eth00x.connTimeout)
				eth00x.connTimeout = setTimeout(function(){eth00x.connect()}, 1000);
				break;
			
			case connStates.TIMEOUT: //Geen antwoord
				CF.setSystemProperties(eth00x.systemName,{enabled: false});
				CF.setSystemProperties(eth00x.systemName,{enabled: true});
				clearTimeout(eth00x.connTimeout)
				eth00x.connTimeout = setTimeout(function(){eth00x.connect()}, 3000);
		}
 	},

    parseData : function (fbName, rawdata) { 
 		eth00x.connect(connStates.OK)
    	var matches = rawdata.match(/(.*)/);
	    if(!matches){ CF.log("no ETH00X RAW Return match"); return;}
	    var byteVal = rawdata.charCodeAt(4);
		switch(eth00x.commState){

			case commStates.RESULT_GET:
				//CF.log(  eth00x.makeReadable(null, rawdata) )

				for(i = 0; i <= 7; i++ ){ // onderzoek elke bit van byteval
					eth00x.relayStates[i] = (byteVal/2) != Math.trunc(byteVal/2);
					byteVal = Math.trunc(byteVal/2);
				}

				dispatchEvent( // Na vaststellen van relaistatus een event triggeren
					new CustomEvent('relayUpdate', {
						detail: {
							fbName: fbName,
							relayStates: eth00x.relayStates
						},
					})
				)
				
				if(eth00x.buffer.length > 0) { //Als er nog commando's gebufferd zijn de volgende versturen
					CF.send(eth00x.systemName, eth00x.buffer.shift());
					eth00x.commState = commStates.RESULT_SET
				} else { //Anders is de zender vrij
					eth00x.commState = commStates.IDLE
				}
					
			break;

			case commStates.RESULT_SET:
				eth00x.requestStatus()
			break;
			
			default:
				buffer.length = 0
				eth00x.commState = commStates.IDLE
			break;
			
		}
		
     },

	makeReadable : function (feedbackItem, matchedString){
		var readable = "", i;
		
		for (i = 0; i < matchedString.length; i++) {
			var byteVal = matchedString.charCodeAt(i);
			
			if (byteVal < 32 || byteVal > 127) {
				readable += "\\x" + ("0" + byteVal.toString(16).toUpperCase()).slice(-2);
			} else {
				readable += matchedString[i];
			}
		}
		return readable;
	},

}; //var demo = {
CF.modules.push({name:"ETH00X Module", setup:eth00x.init});




var deursignalering = {
    buttons             : ["d6001","d6002","d6003","d6004","d6005","d6006","d6007","d6008"], //inschakelen, hoog/laag

    init : function(){
        CF.watch(CF.ObjectPressedEvent, deursignalering.buttons, deursignalering.control);      // Watch of een button wordt ingedrukt
        addEventListener('relayUpdate', deursignalering.updatelamps)
    },

    updatelamps : function(relayUpdateEvent) {

        CF.setJoin("d6001", relayUpdateEvent.detail.relayStates[0])


        for(i = 3; i <= 5; i++ ){

            if (relayUpdateEvent.detail.relayStates[i - 1]){
                CF.setJoin("d600" + i, relayUpdateEvent.detail.relayStates[i - 1])
            }
        }



    },

    control : function(join, value, tokens, tags){
        CF.getJoin(join, function(getJoin,getValue,getTokens){
            
            
            switch(getJoin){

                case "d6001" :
                    eth00x.switchRelais(
                        String.fromCharCode(getJoin.slice(-1).charCodeAt(0)-48),
                        !getValue
                    )
                break;

                default :
                    CF.setJoin(getJoin,0)
                break;

            }




        })
    },

}
CF.modules.push({name: "main/Ventilatie Module", setup: deursignalering.init});

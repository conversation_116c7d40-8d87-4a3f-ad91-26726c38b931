﻿<?xml version="1.0" encoding="utf-8"?>
<gui>
  <properties>
    <project>Nunspeet HHG</project>
    <designer>LRU</designer>
    <size autoposition="scale">
      <portrait width="800" height="1280" />
      <landscape width="1280" height="800" />
    </size>
    <autoposition />
    <imagefolder>
    </imagefolder>
    <debug loaderrors="0" connectionerrors="0" />
    <devices>
      <device name="ZDMID APPC-10SLB testscherm PKO">64066DBBEA419A8D7D772EF5B7BF8F5B</device>
      <device name="Middelburg - EbehHaezer - Cedrah">188BAA738779AB0C4382ABE73F0C69A6</device>
      <device name="Poortugaal - GG">12103E6520FF3A8DD5C65DDCBE193F96</device>
      <device name="GG - Ridderkerk">776A4B5DD2D9458169EB8D8755C8647C</device>
      <device name="HHG Nunspeet">8CE377887C3C37736F11532B80F55EFC</device>
    </devices>
  </properties>
  <systems>
    <system name="Nexia" protocol="tcp" accept="0" ip="***************" port="23" origin="0" alwayson="1" idleTimeout="0" dialogTimeout="0" heartbeatMode="-1" heartbeatRx="" heartbeatTx="" textEncoding="" eom="\x0D\x0A" js="" connectionStatus="21" disconnectionStatus="22" startupCmd="" startupMacro="" offlinequeue="1" ssl="0">
      <fb name="raw_Nexia" regex="." />
    </system>
  </systems>
  <themes>
    <theme type="background" name=".Background"><![CDATA[ background-image: url(Aluminum.png); background-repeat: no-repeat;]]></theme>
    <theme type="gauge" name=".Slider[state='0']"><![CDATA[]]></theme>
    <theme type="gauge" name=".Slider[state='1']"><![CDATA[]]></theme>
    <theme type="text" name=".LCD_text_bold"><![CDATA[ text-shadow: rgba(0,0,0,0.06) 1px 2px 3px; color: #000000; font-size: 18px; font-name: 'Helvetica-Bold'; font-family: 'Helvetica'; text-align: center; vertical-align: top; font-weight: bold; font-style: none; text-decoration: none; display: table-cell;]]></theme>
    <theme type="text" name=".Text"><![CDATA[ color: #D4D4D4; font-size: 14px; font-family: 'Arial'; text-align: right; vertical-align: middle; font-weight: normal; font-style: none; text-decoration: none; display: table-cell;]]></theme>
    <theme type="text" name=".LCD_text"><![CDATA[ text-shadow: rgba(0,0,0,0.06) 1px 2px 3px; color: #FFFFFF; font-size: 16px; font-name: 'Helvetica'; font-family: 'Helvetica'; text-align: left; vertical-align: top; font-weight: normal; font-style: none; text-decoration: none; display: table-cell;]]></theme>
    <theme type="text" name=".Title"><![CDATA[ color: #D8D8D8; font-size: 30px; font-name: 'Helvetica-Bold'; font-family: 'Helvetica'; text-align: center; vertical-align: middle; font-weight: bold; font-style: none; text-decoration: none; display: table-cell;]]></theme>
    <theme type="text" name=".subTitle"><![CDATA[ color: #D8D8D8; font-size: 24px; font-name: 'Helvetica-BoldOblique'; font-family: 'Helvetica'; text-align: right; vertical-align: middle; font-weight: bold; font-style: italics; text-decoration: none; display: table-cell;]]></theme>
    <theme type="text" name=".Status"><![CDATA[ color: #D4D4D4; font-size: 14px; font-name: 'Arial-BoldMT'; font-family: 'Arial'; text-align: left; vertical-align: top; font-weight: bold; font-style: none; text-decoration: none; display: table-cell;]]></theme>
    <theme type="gauge" name=".INPUTLEVEL[state='0']"><![CDATA[ background-image: url(BAR_FULL_OFF.png);]]></theme>
    <theme type="gauge" name=".INPUTLEVEL[state='1']"><![CDATA[ background-image: url(BAR_FULL.png);]]></theme>
    <theme type="background" name=".portrait"><![CDATA[ background-image: url(bg_portrait.png); background-repeat: no-repeat;]]></theme>
    <theme type="background" name=".portrait_1"><![CDATA[ background-image: url(<EMAIL>); background-repeat: no-repeat;]]></theme>
    <theme type="input" name=".InputField"><![CDATA[ background-color: #FFFFFF; border-width: 0px; border-color: #292929; color: #414141; font-size: 20px; font-name: 'Helvetica-Bold'; font-family: 'Helvetica'; text-align: left; vertical-align: middle; font-weight: bold; font-style: none; text-decoration: none; display: table-cell;]]></theme>
    <theme type="gauge" name=".INPUTLEVEL_1[state='0']"><![CDATA[ background-image: url(BAR_FULL_OFF.png);]]></theme>
    <theme type="gauge" name=".INPUTLEVEL_1[state='1']"><![CDATA[ background-image: url(BAR_FULL.png);]]></theme>
    <theme type="gauge" name=".cfblue2_vol[state='0']"><![CDATA[ background-image: url(vol_bar_off.png);]]></theme>
    <theme type="gauge" name=".cfblue2_vol[state='1']"><![CDATA[ background-image: url(vol_bar_on.png);]]></theme>
    <theme type="text" name=".newtheme_1"><![CDATA[ color: #FFFFFF; font-size: 28px; font-family: 'Arial'; text-align: center; vertical-align: top; font-weight: normal; font-style: none; text-decoration: none; display: table-cell;]]></theme>
    <theme type="button" name=".Indicator[state='0']"><![CDATA[padding: 0px 0px 0px 0px; background-image: url(LED-Red.png); color: #FFFFFF; font-size: 12px; font-family: 'Verdana'; font-weight: normal; font-style: none; text-decoration: none; text-align: center; vertical-align: middle; display: table-cell; box-sizing: border-box; -webkit-box-sizing: border-box; -webkit-tap-highlight-color:rgba(0,0,0,0);]]></theme>
    <theme type="button" name=".Indicator[state='1']"><![CDATA[padding: 0px 0px 0px 0px; background-image: url(LED-Green.png); color: #FFFFFF; font-size: 12px; font-family: 'Verdana'; font-weight: normal; font-style: none; text-decoration: none; text-align: center; vertical-align: middle; display: table-cell; box-sizing: border-box; -webkit-box-sizing: border-box; -webkit-tap-highlight-color:rgba(0,0,0,0);]]></theme>
    <theme type="text" name=".newtheme_2"><![CDATA[ color: #FFFFFF; font-size: 20px; font-family: 'Arial'; text-align: left; vertical-align: top; font-weight: normal; font-style: none; text-decoration: none; display: table-cell;]]></theme>
    <theme type="background" name=".portrait_2"><![CDATA[ background-image: url(<EMAIL>); background-repeat: no-repeat;]]></theme>
    <theme type="button" name=".Button_aan_uit[state='0']"><![CDATA[padding: 0px 0px 0px 0px; background-image: url(Matrix-selector-On.png); text-shadow: rgba(0,0,0,0.08) 1px 3px 3px; color: #FFFFFF; font-size: 18px; font-name: 'Helvetica-Bold'; font-family: 'Helvetica'; font-weight: bold; font-style: none; text-decoration: none; text-align: center; vertical-align: middle; display: table-cell; box-sizing: border-box; -webkit-box-sizing: border-box; -webkit-tap-highlight-color:rgba(0,0,0,0);]]></theme>
    <theme type="button" name=".Button_aan_uit[state='1']"><![CDATA[padding: 0px 0px 0px 0px; background-image: url(generic button OFF.png); text-shadow: rgba(0,0,0,0.08) 1px 3px 3px; color: #FFFFFF; font-size: 14px; font-name: 'Helvetica-Bold'; font-family: 'Helvetica'; font-weight: bold; font-style: none; text-decoration: none; text-align: center; vertical-align: middle; display: table-cell; box-sizing: border-box; -webkit-box-sizing: border-box; -webkit-tap-highlight-color:rgba(0,0,0,0);]]></theme>
    <theme type="button" name=".Indicator_1[state='0']"><![CDATA[padding: 0px 0px 0px 0px; color: #FFFFFF; font-size: 12px; font-family: 'Verdana'; font-weight: normal; font-style: none; text-decoration: none; text-align: center; vertical-align: middle; display: table-cell; box-sizing: border-box; -webkit-box-sizing: border-box; -webkit-tap-highlight-color:rgba(0,0,0,0);]]></theme>
    <theme type="button" name=".Indicator_1[state='1']"><![CDATA[padding: 0px 0px 0px 0px; background-image: url(LED-Green.png); color: #FFFFFF; font-size: 12px; font-family: 'Verdana'; font-weight: normal; font-style: none; text-decoration: none; text-align: center; vertical-align: middle; display: table-cell; box-sizing: border-box; -webkit-box-sizing: border-box; -webkit-tap-highlight-color:rgba(0,0,0,0);]]></theme>
    <theme type="button" name=".Buttonzonderplaatje[state='0']"><![CDATA[padding: 0px 0px 0px 0px; background-image: url(Wesotronic-Logo-audiovisueel_CMYK.png); text-shadow: rgba(0,0,0,0.08) 1px 3px 3px; color: #FFFFFF; font-size: 14px; font-name: 'Helvetica-Bold'; font-family: 'Helvetica'; font-weight: bold; font-style: none; text-decoration: none; text-align: center; vertical-align: middle; display: table-cell; box-sizing: border-box; -webkit-box-sizing: border-box; -webkit-tap-highlight-color:rgba(0,0,0,0);]]></theme>
    <theme type="button" name=".Buttonzonderplaatje[state='1']"><![CDATA[padding: 0px 0px 0px 0px; background-image: url(Wesotronic-Logo-audiovisueel_CMYK.png); text-shadow: rgba(0,0,0,0.08) 1px 3px 3px; color: #FFFFFF; font-size: 14px; font-name: 'Helvetica-Bold'; font-family: 'Helvetica'; font-weight: bold; font-style: none; text-decoration: none; text-align: center; vertical-align: middle; display: table-cell; box-sizing: border-box; -webkit-box-sizing: border-box; -webkit-tap-highlight-color:rgba(0,0,0,0);]]></theme>
    <theme type="background" name=".portrait_3"><![CDATA[ background-image: url(<EMAIL>); background-repeat: no-repeat;]]></theme>
    <theme type="button" name=".Button_aan_uit_INV[state='0']"><![CDATA[padding: 0px 0px 0px 0px; background-image: url(generic button OFF.png); text-shadow: rgba(0,0,0,0.08) 1px 3px 3px; color: #FFFFFF; font-size: 14px; font-name: 'Helvetica-Bold'; font-family: 'Helvetica'; font-weight: bold; font-style: none; text-decoration: none; text-align: center; vertical-align: middle; display: table-cell; box-sizing: border-box; -webkit-box-sizing: border-box; -webkit-tap-highlight-color:rgba(0,0,0,0);]]></theme>
    <theme type="button" name=".Button_aan_uit_INV[state='1']"><![CDATA[padding: 0px 0px 0px 0px; background-image: url(Matrix-selector-On.png); text-shadow: rgba(0,0,0,0.08) 1px 3px 3px; color: #FFFFFF; font-size: 14px; font-name: 'Helvetica-Bold'; font-family: 'Helvetica'; font-weight: bold; font-style: none; text-decoration: none; text-align: center; vertical-align: middle; display: table-cell; box-sizing: border-box; -webkit-box-sizing: border-box; -webkit-tap-highlight-color:rgba(0,0,0,0);]]></theme>
  </themes>
  <scripts>
    <script name="main.js" />
    <script name="nexia.js" />
  </scripts>
  <page name="Startup" folder="" j="0" tags="" transition="None" subtype="None" time="0" ease="" start="1">
    <portrait />
    <landscape t="portrait_1">
      <img x="10" y="4" w="322" h="792" j="0" clickthrough="0" refresh="0" l="0" noCache="0" downloadIndicator="0" clearOnURLChange="0" tags="">kader 400x590 75 procent.png</img>
      <img x="468" y="4" w="186" h="792" j="0" clickthrough="0" refresh="0" l="0" noCache="0" downloadIndicator="0" clearOnURLChange="0" tags="">kader 400x590 75 procent.png</img>
      <btn j="101201" x="22" y="671" w="120" h="100" t="Button_aan_uit" overrideFontAlignmentH="center" overrideFontAlignmentV="center" flip="None" sim="0" l="0" s="1" tags="">
        <inactive s="0">Lipmic</inactive>
        <active s="0">Lipmic</active>
      </btn>
      <txt j="101201" x="53" y="637" w="74" h="20" t="LCD_text" wrap="False" l="0" tags="">Value</txt>
      <img x="48" y="20" w="100" h="610" j="0" clickthrough="1" refresh="0" l="0" noCache="0" downloadIndicator="0" clearOnURLChange="0" tags="">Empty-Slide-Track.png</img>
      <slider j="101201" d="0" x="46" y="54" w="100" h="552" min="-100" max="0" decimals="0" unit="decimal" t="Slider" sim="1" l="0" tags="">
        <indicator state="0" offsetX="-25" offsetY="10" x="0" y="0" w="0" h="0" imgW="0" imgH="0" t="">IN-OUT-Slider-Knob.png</indicator>
        <indicator state="1" offsetX="-25" offsetY="10" x="0" y="0" w="0" h="0" imgW="0" imgH="0" t="">IN-OUT-Slider-Knob.png</indicator>
      </slider>
      <btn j="101202" x="189" y="671" w="120" h="100" t="Button_aan_uit" overrideFontAlignmentH="center" overrideFontAlignmentV="center" flip="None" sim="0" l="0" s="1" tags="">
        <inactive s="0">Rever</inactive>
        <active s="0">Rever</active>
      </btn>
      <txt j="101202" x="220" y="637" w="74" h="20" t="LCD_text" wrap="False" l="0" tags="">Value</txt>
      <img x="215" y="20" w="100" h="610" j="0" clickthrough="1" refresh="0" l="0" noCache="0" downloadIndicator="0" clearOnURLChange="0" tags="">Empty-Slide-Track.png</img>
      <slider j="101202" d="0" x="212" y="54" w="100" h="552" min="-100" max="0" decimals="0" unit="decimal" t="Slider" sim="1" l="0" tags="">
        <indicator state="0" offsetX="-25" offsetY="10" x="0" y="0" w="0" h="0" imgW="0" imgH="0" t="">IN-OUT-Slider-Knob.png</indicator>
        <indicator state="1" offsetX="-25" offsetY="10" x="0" y="0" w="0" h="0" imgW="0" imgH="0" t="">IN-OUT-Slider-Knob.png</indicator>
      </slider>
      <btn j="101203" x="500" y="671" w="120" h="100" t="Button_aan_uit" overrideFontAlignmentH="center" overrideFontAlignmentV="center" flip="None" sim="0" l="0" s="1" tags="">
        <inactive s="0">Sfeer</inactive>
        <active s="0">Sfeer</active>
      </btn>
      <txt j="101203" x="531" y="637" w="74" h="20" t="LCD_text" wrap="False" l="0" tags="">Value</txt>
      <img x="526" y="20" w="100" h="610" j="0" clickthrough="1" refresh="0" l="0" noCache="0" downloadIndicator="0" clearOnURLChange="0" tags="">Empty-Slide-Track.png</img>
      <slider j="101203" d="0" x="523" y="54" w="100" h="552" min="-18" max="0" decimals="0" unit="decimal" t="Slider" sim="1" l="0" tags="">
        <indicator state="0" offsetX="-25" offsetY="10" x="0" y="0" w="0" h="0" imgW="0" imgH="0" t="">IN-OUT-Slider-Knob.png</indicator>
        <indicator state="1" offsetX="-25" offsetY="10" x="0" y="0" w="0" h="0" imgW="0" imgH="0" t="">IN-OUT-Slider-Knob.png</indicator>
      </slider>
      <btn j="100" x="1250" y="0" w="30" h="30" t="Indicator_1" overrideFontAlignmentH="center" overrideFontAlignmentV="center" flip="None" sim="0" l="0" s="1" tags="">
        <inactive s="0">
        </inactive>
        <active s="0">
        </active>
      </btn>
      <btn j="0" x="1153" y="767" w="122" h="33" t="Buttonzonderplaatje" overrideFontAlignmentH="center" overrideFontAlignmentV="center" flip="settings" sim="0" l="0" s="1" tags="">
        <inactive s="0">
        </inactive>
        <active s="0">
        </active>
      </btn>
    </landscape>
  </page>
  <page name="settings" folder="" j="0" tags="" transition="None" subtype="None" time="0" ease="">
    <portrait />
    <landscape t="portrait_1">
      <img x="890" y="680" w="390" h="120" j="0" clickthrough="0" refresh="0" l="0" noCache="0" downloadIndicator="0" clearOnURLChange="0" tags="">kader 400x590 75 procent.png</img>
      <btn j="0" x="973" y="706" w="250" h="70" t="Buttonzonderplaatje" overrideFontAlignmentH="center" overrideFontAlignmentV="center" flip="Startup" sim="0" l="0" s="1" tags="">
        <inactive s="0">
        </inactive>
        <active s="0">
        </active>
      </btn>
      <btn j="102" x="185" y="64" w="20" h="20" t="Indicator" overrideFontAlignmentH="center" overrideFontAlignmentV="center" flip="None" sim="0" l="0" s="1" tags="">
        <inactive s="0">
        </inactive>
        <active s="0">
        </active>
      </btn>
      <txt j="103" x="247" y="64" w="270" h="26" t="Text" wrap="False" l="0" tags="" overrideFontAlignmentH="left" overrideFontAlignmentV="center">New Text Object</txt>
      <txt j="0" x="63" y="64" w="100" h="28" t="Text" wrap="False" l="0" tags="">Audio DSP</txt>
      <btn j="0" x="1150" y="0" w="130" h="100" t="Button_aan_uit" overrideFontAlignmentH="center" overrideFontAlignmentV="center" flip="None" sim="0" l="0" s="1" tags="" js="CF.loadGUI(&quot;&quot;, { reloadGUI: true, reloadAssets: true });">
        <inactive s="0">Update</inactive>
        <active s="0">Update</active>
      </btn>
      <txt j="101" x="247" y="14" w="270" h="26" t="Text" wrap="False" l="0" tags="" overrideFontAlignmentH="left" overrideFontAlignmentV="center">New Text Object</txt>
      <txt j="0" x="7" y="14" w="156" h="28" t="Text" wrap="False" l="0" tags="">Verbinding ProDVX</txt>
      <btn j="103205" x="583" y="671" w="120" h="100" t="Button_aan_uit" overrideFontAlignmentH="center" overrideFontAlignmentV="center" flip="None" sim="0" l="0" s="1" tags="">
        <inactive s="0">Piano</inactive>
        <active s="0">Piano</active>
      </btn>
      <txt j="103205" x="614" y="638" w="74" h="20" t="LCD_text" wrap="False" l="0" tags="">Value</txt>
      <img x="609" y="21" w="100" h="610" j="0" clickthrough="1" refresh="0" l="0" noCache="0" downloadIndicator="0" clearOnURLChange="0" tags="">Empty-Slide-Track.png</img>
      <slider j="103207" d="0" x="606" y="55" w="100" h="552" min="-18" max="0" decimals="0" unit="decimal" t="Slider" sim="1" l="0" tags="">
        <indicator state="0" offsetX="-25" offsetY="10" x="0" y="0" w="0" h="0" imgW="0" imgH="0" t="">IN-OUT-Slider-Knob.png</indicator>
        <indicator state="1" offsetX="-25" offsetY="10" x="0" y="0" w="0" h="0" imgW="0" imgH="0" t="">IN-OUT-Slider-Knob.png</indicator>
      </slider>
      <btn j="103206" x="745" y="671" w="120" h="100" t="Button_aan_uit" overrideFontAlignmentH="center" overrideFontAlignmentV="center" flip="None" sim="0" l="0" s="1" tags="">
        <inactive s="0">Podium
L/R</inactive>
        <active s="0">Podium
L/R</active>
      </btn>
      <txt j="103206" x="776" y="638" w="74" h="20" t="LCD_text" wrap="False" l="0" tags="">Value</txt>
      <img x="771" y="21" w="100" h="610" j="0" clickthrough="1" refresh="0" l="0" noCache="0" downloadIndicator="0" clearOnURLChange="0" tags="">Empty-Slide-Track.png</img>
      <slider j="103206" d="0" x="768" y="55" w="100" h="552" min="-18" max="0" decimals="0" unit="decimal" t="Slider" sim="1" l="0" tags="">
        <indicator state="0" offsetX="-25" offsetY="10" x="0" y="0" w="0" h="0" imgW="0" imgH="0" t="">IN-OUT-Slider-Knob.png</indicator>
        <indicator state="1" offsetX="-25" offsetY="10" x="0" y="0" w="0" h="0" imgW="0" imgH="0" t="">IN-OUT-Slider-Knob.png</indicator>
      </slider>
    </landscape>
  </page>
  <subpage name="List" folder="" w="300" h="25" clip="1">
    <list j="1" x="0" y="79" w="196" h="501" headerSub="" titleSub="" contentSub="List" footerSub="" orientation="v" l="0" swipedelete="0" tags="" />
    <txt j="0" x="99" y="502" w="100" h="100" t="LCD_text" wrap="False" l="0" tags="">New Text Object</txt>
    <txt j="2" x="0" y="0" w="300" h="80" t="newtheme_2" wrap="False" l="0" tags="">New Text Object</txt>
  </subpage>
</gui>
var LastGETCommand = "0"; //Deze nog verhuizen naar nexia.JS
var LastGETJOIN	= "0";  //Deze nog verhuizen naar nexia.JS


function Enum(constantsList) {
    for (var i in constantsList) {
        this[constantsList[i]] = i;
    }
}

var commStates = new Enum(['IDLE', 'RESULT_SET', 'RESULT_GET', 'BUSY'])
var connStates = new Enum(['IDLE', 'OK', 'KICK', 'TIMEOUT'])

CF.userMain = function() {

	CF.log("JS working!, main.JS");
	setTimeout({},200);
    CF.watch(CF.NetworkStatusChangeEvent, onNetworkStatusChange, true);
}

function onNetworkStatusChange(networkStatus) {
    if (networkStatus.hasNetwork) {
        // note that the device may not have an IPv6 address,
        // in which case the IPv6 address string will be empty
        CF.setJoin("s101", "IP: " +networkStatus.ipv4address + " via: " + networkStatus.networkType);
    } else {
        CF.setJoin("s101", "No network");
    }
}


/*
function makeReadable(feedbackItem, matchedString){
     var readable = "", i;
    for (i = 0; i < matchedString.length; i++) {
        var byteVal = matchedString.charCodeAt(i);
        if (byteVal < 32 || byteVal > 127) {
            readable += "\\x" + ("0" + byteVal.toString(16).toUpperCase()).slice(-2);
        } else {
            readable += matchedString[i];
        }
    }
    
    return readable;
    
  }
*/

var helderheid = {

    buttons : ["d11","d12"], // buttons voor aanpassen helderheid!

    init : function(){      
      CF.log("Helderheid (brightness) initialized");
      CF.watch(CF.DevicePropertyChangeEvent, CF.ScreenBrightnessProperty, helderheid.onPropertyChange);

      CF.watch(CF.ObjectPressedEvent, helderheid.buttons, helderheid.adjustBrightness);


    }, //Init :

    onPropertyChange : function (property, value) {
        if (property == CF.ScreenBrightnessProperty) {
            CF.log("New screen brightness: " + value);
            CF.setJoin("s10", "=newBrightness: " + value)
        } else if (property == CF.SoundOutputVolumeProperty) {
            CF.log("New sound volume: " + value);
        }
    },

    adjustBrightness : function (join, value, tokens, tags){
//        CF.log("In adjustBrightness!")
//        CF.log("actual Brightness: " + CF.device.screenBrightness)

        var currentBrightness = CF.device.screenBrightness;

        CF.getJoin(join, function(getJoin,getValue,getTokens){

            switch(join){
                case helderheid.buttons[0] :
//                    CF.log("Helderheid buttons 1");
                    var newBrightness = currentBrightness - 0.1;
                    
                    if (newBrightness <= 0){
                        newBrightness = 0;
                    }; // if (newBrightness <= 0)


                    CF.setDeviceProperty(CF.ScreenBrightnessProperty, newBrightness);
                break;

                case helderheid.buttons[1] :
//                    CF.log("Helderheid buttons 2");
                    var newBrightness = currentBrightness + 0.1;
                    
                    if (newBrightness >= 1){
                        newBrightness = 1;
                    }; // if (newBrightness <= 0)
                    
                    CF.setDeviceProperty(CF.ScreenBrightnessProperty, newBrightness);
                break;

                default : 
                    CF.log("Hier magtie niet komen! Helderheid.adjustBrightness");
                break;
            }; // switch(join){};
        }); // getJoin
    },
}; // var helderheid = {}

CF.modules.push({name:"CF.Helderheid Module", setup:helderheid.init});

